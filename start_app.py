#!/usr/bin/env python3
"""
Simple startup script for TgIndex Pro
Sets environment variables and starts the application
"""

import os
import sys

def main():
    print("🚀 Starting TgIndex Pro...")
    
    # Set environment variables (these will override config.json values)
    os.environ["API_ID"] = "24522627"
    os.environ["API_HASH"] = "d20c1537e9dc685ddcdb66aac7442cce"
    os.environ["SESSION_STRING"] = "1BVtsOJUBuxj1GLvqA_KjSu5_Mga9H1t_eCehPzPmKpHxGCNs0XeGjVF3WjnflWbsosQIIrf9BqtCsnrNSWVxvOyOaQrUpSHz4Smm1qZZdQEWQ3wAOCAT4FeG-PeeHandVgG21ZJN02L71LjLapc-31REL8HeOLty2aheamfutPLLgI4sCVNDjKUQTptM90tOte3FFzpvcdma0GYEPalLi3LOAYT4hP28RPqYLIvhbpyxK3D1FySNaYey9XYVPn7Xq6dhfcApb81QaNcri12ZEvpUcdhRl3p-HiYzpWPyvbQRYa_NIQZEl5lN3nroAc8WsfvM8UwBJKCbjLt4Uifh0XXcQr3_IxM="
    os.environ["INDEXING_CHAT"] = "-1002788057044"
    os.environ["HOST"] = "0.0.0.0"
    os.environ["PORT"] = "8080"
    os.environ["DEBUG"] = "false"
    
    print("✅ Environment variables set")
    
    # Import and run the app
    try:
        from app.main import Indexer
        print("✅ Modules imported successfully")
        
        indexer = Indexer()
        print("✅ Indexer created")
        print("🌐 Starting web server on http://0.0.0.0:8080")
        print("📱 Access your Telegram index at: http://localhost:8080")
        print("🛑 Press Ctrl+C to stop the server")
        
        indexer.run()
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
