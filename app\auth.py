"""
Authentication system for TgIndex Pro
Handles login, session management, and access control
"""

import hashlib
import secrets
import time
from typing import Optional, Dict, Any
from aiohttp import web
from aiohttp_session import get_session, new_session
import aiohttp_jinja2


class AuthManager:
    """Manages authentication and authorization"""
    
    def __init__(self, config):
        self.config = config
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.session_timeout = 24 * 60 * 60  # 24 hours
    
    def hash_password(self, password: str) -> str:
        """Hash password with salt"""
        salt = self.config.get("auth.salt", "tgindex_default_salt")
        return hashlib.sha256((password + salt).encode()).hexdigest()
    
    def verify_credentials(self, username: str, password: str) -> bool:
        """Verify username and password against config"""
        auth_users = self.config.get("auth.users", {})
        
        if username not in auth_users:
            return False
        
        stored_hash = auth_users[username]
        provided_hash = self.hash_password(password)
        
        return stored_hash == provided_hash
    
    def create_session(self, username: str) -> str:
        """Create a new session token"""
        session_token = secrets.token_urlsafe(32)
        self.active_sessions[session_token] = {
            'username': username,
            'created_at': time.time(),
            'last_activity': time.time()
        }
        return session_token
    
    def validate_session(self, session_token: str) -> Optional[str]:
        """Validate session token and return username if valid"""
        if not session_token or session_token not in self.active_sessions:
            return None
        
        session_data = self.active_sessions[session_token]
        current_time = time.time()
        
        # Check if session has expired
        if current_time - session_data['created_at'] > self.session_timeout:
            del self.active_sessions[session_token]
            return None
        
        # Update last activity
        session_data['last_activity'] = current_time
        return session_data['username']
    
    def invalidate_session(self, session_token: str):
        """Invalidate a session"""
        if session_token in self.active_sessions:
            del self.active_sessions[session_token]
    
    def cleanup_expired_sessions(self):
        """Remove expired sessions"""
        current_time = time.time()
        expired_tokens = []
        
        for token, data in self.active_sessions.items():
            if current_time - data['created_at'] > self.session_timeout:
                expired_tokens.append(token)
        
        for token in expired_tokens:
            del self.active_sessions[token]


def require_auth(auth_manager: AuthManager):
    """Decorator to require authentication for routes"""
    def decorator(handler):
        async def wrapper(self, request):
            # Check if authentication is enabled
            if not self.config.get("auth.enabled", False):
                return await handler(self, request)
            
            # Get session from cookie
            session_token = request.cookies.get('tgindex_session')
            username = auth_manager.validate_session(session_token)
            
            if not username:
                # Redirect to login page
                if request.path.startswith('/api/'):
                    return web.json_response({'error': 'Authentication required'}, status=401)
                else:
                    raise web.HTTPFound('/login')
            
            # Add username to request for use in handlers
            request.username = username
            return await handler(self, request)
        
        return wrapper
    return decorator


class AuthViews:
    """Authentication-related view handlers"""
    
    def __init__(self, auth_manager: AuthManager, config):
        self.auth_manager = auth_manager
        self.config = config
    
    @aiohttp_jinja2.template('login.html')
    async def login_page(self, request):
        """Display login page"""
        if not self.config.get("auth.enabled", False):
            raise web.HTTPFound('/')
        
        error = request.query.get('error')
        return {'error': error}
    
    async def login_submit(self, request):
        """Handle login form submission"""
        if not self.config.get("auth.enabled", False):
            raise web.HTTPFound('/')
        
        data = await request.post()
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        
        if not username or not password:
            raise web.HTTPFound('/login?error=Please enter both username and password')
        
        if self.auth_manager.verify_credentials(username, password):
            # Create session
            session_token = self.auth_manager.create_session(username)
            
            # Set cookie and redirect
            response = web.HTTPFound('/')
            response.set_cookie(
                'tgindex_session', 
                session_token,
                max_age=24*60*60,  # 24 hours
                httponly=True,
                secure=request.secure
            )
            return response
        else:
            raise web.HTTPFound('/login?error=Invalid username or password')
    
    async def logout(self, request):
        """Handle logout"""
        session_token = request.cookies.get('tgindex_session')
        if session_token:
            self.auth_manager.invalidate_session(session_token)
        
        response = web.HTTPFound('/login')
        response.del_cookie('tgindex_session')
        return response
    
    async def status(self, request):
        """API endpoint to check authentication status"""
        session_token = request.cookies.get('tgindex_session')
        username = self.auth_manager.validate_session(session_token)
        
        return web.json_response({
            'authenticated': username is not None,
            'username': username,
            'auth_enabled': self.config.get("auth.enabled", False)
        })


def setup_auth_routes(app, auth_views):
    """Setup authentication routes"""
    app.router.add_get('/login', auth_views.login_page)
    app.router.add_post('/login', auth_views.login_submit)
    app.router.add_get('/logout', auth_views.logout)
    app.router.add_get('/api/auth/status', auth_views.status)
