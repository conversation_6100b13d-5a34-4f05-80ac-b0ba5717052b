import random
import string
import logging

from aiohttp import web

from .config import index_settings, alias_ids, chat_ids


log = logging.getLogger(__name__)


def generate_alias_id(chat, original_chat_id=None):
    # Use the original chat_id if provided, otherwise use chat.id
    chat_id = original_chat_id if original_chat_id is not None else chat.id
    title = chat.title
    while True:
        alias_id = ''.join([random.choice(string.ascii_letters + string.digits) for _ in range(len(str(abs(chat_id))))])
        if alias_id in alias_ids:
            continue
        alias_ids.append(alias_id)
        chat_ids.append({
            'chat_id': chat_id,  # Store the original chat_id format
            'alias_id': alias_id,
            'title': title
        })
        print(f"✅ Generated alias {alias_id} for chat_id {chat_id} ({title})")
        return alias_id


async def setup_routes(app, handler):
    h = handler
    client = h.client
    p = r"/{chat:[^/]+}"
    routes =  [
        web.get('/', h.home),
        web.get('/debug', h.debug_info),
        web.get('/test-messages', h.test_messages),
        web.post('/otg', h.dynamic_view),
        web.get('/otg', h.otg_view),
        web.get('/pc',h.playlist_creator),
        web.get(p, h.index),
        web.get(p + r"/logo", h.logo),
        web.get(p + r"/{id:\d+}/view", h.info),
        web.get(p + r"/{id:\d+}/download", h.download_get),
        web.get(p + r"/{id:\d+}/thumbnail", h.thumbnail_get),

        #below routes work without alias id
        web.get(r"/{id:\d+}/", h.download_get),
        web.get(r"/{id:\d+}/view", h.info),
        web.get(r"/{id:\d+}/v.mp4", h.download_get),
        web.view(r'/{wildcard:.*}', h.wildcard)
    ]
    index_all = index_settings['index_all']
    index_private = index_settings['index_private']
    index_group = index_settings['index_group']
    index_channel = index_settings['index_channel']
    exclude_chats = index_settings['exclude_chats']
    include_chats = index_settings['include_chats']
    if index_all:
        async for chat in client.iter_dialogs():
            alias_id = None
            if chat.id in exclude_chats:
                continue

            if chat.is_user:
                if index_private:
                    alias_id = generate_alias_id(chat)
            elif chat.is_channel:
                if index_channel:
                    alias_id = generate_alias_id(chat)
            else:
                if index_group:
                    alias_id = generate_alias_id(chat)

            if not alias_id:
                continue
            log.debug(f"Index added for {chat.id} :: {chat.title} at /{alias_id}")

    else:
        print(f"🔄 Setting up routes for {len(include_chats)} chats: {include_chats}")
        for chat_id in include_chats:
            try:
                print(f"🔄 Getting entity for chat_id: {chat_id}")
                chat = await client.get_entity(chat_id)
                # Pass the original chat_id to maintain the correct format
                alias_id = generate_alias_id(chat, original_chat_id=chat_id)
                print(f"✅ Index added for {chat_id} :: {chat.title} at /{alias_id}")
                log.debug(f"Index added for {chat_id} :: {chat.title} at /{alias_id}")
            except Exception as e:
                print(f"❌ Failed to get entity for {chat_id}: {e}")
                log.error(f"Failed to get entity for {chat_id}: {e}")

    print(f"📊 Total chat_ids configured: {len(chat_ids)}")
    print(f"📊 Chat IDs: {chat_ids}")
    app.add_routes(routes)
