{% include 'header.html' %}
            
            <div class="block md:flex justify-between items-center px-4 text-center border-b-2">
                <div class="m-2 block md:flex items-center justify-center md:justify-start text-2xl md:text-right font-bold text-blue-900">
                    <img class="mx-auto md:ml-0 md:mr-1 my-2 w-16 h-16 rounded-full bg-black" src="{{logo}}">
                    <h1> {{name}} </h1>
                </div>
                
                <div class="m-2">
                    <form class="">
                        <input class="border rounded px-4 py-2 border-indigo-500 placeholder-indigo-500 text-indigo-500 outline-none" type="text" name="search" value="{% if search %}{{search}}{% endif %}" placeholder="search...">
                    </form>
                </div>
                
            </div>
            
            {% if item_list %}
            <script>
      (function(a,b){if("function"==typeof define&&define.amd)define([],b);else if("undefined"!=typeof exports)b();else{b(),a.FileSaver={exports:{}}.exports}})(this,function(){"use strict";function b(a,b){return"undefined"==typeof b?b={autoBom:!1}:"object"!=typeof b&&(console.warn("Deprecated: Expected third argument to be a object"),b={autoBom:!b}),b.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(a.type)?new Blob(["\uFEFF",a],{type:a.type}):a}function c(a,b,c){var d=new XMLHttpRequest;d.open("GET",a),d.responseType="blob",d.onload=function(){g(d.response,b,c)},d.onerror=function(){console.error("could not download file")},d.send()}function d(a){var b=new XMLHttpRequest;b.open("HEAD",a,!1);try{b.send()}catch(a){}return 200<=b.status&&299>=b.status}function e(a){try{a.dispatchEvent(new MouseEvent("click"))}catch(c){var b=document.createEvent("MouseEvents");b.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),a.dispatchEvent(b)}}var f="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof global&&global.global===global?global:void 0,a=/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),g=f.saveAs||("object"!=typeof window||window!==f?function(){}:"download"in HTMLAnchorElement.prototype&&!a?function(b,g,h){var i=f.URL||f.webkitURL,j=document.createElement("a");g=g||b.name||"download",j.download=g,j.rel="noopener","string"==typeof b?(j.href=b,j.origin===location.origin?e(j):d(j.href)?c(b,g,h):e(j,j.target="_blank")):(j.href=i.createObjectURL(b),setTimeout(function(){i.revokeObjectURL(j.href)},4E4),setTimeout(function(){e(j)},0))}:"msSaveOrOpenBlob"in navigator?function(f,g,h){if(g=g||f.name||"download","string"!=typeof f)navigator.msSaveOrOpenBlob(b(f,h),g);else if(d(f))c(f,g,h);else{var i=document.createElement("a");i.href=f,i.target="_blank",setTimeout(function(){e(i)})}}:function(b,d,e,g){if(g=g||open("","_blank"),g&&(g.document.title=g.document.body.innerText="downloading..."),"string"==typeof b)return c(b,d,e);var h="application/octet-stream"===b.type,i=/constructor/i.test(f.HTMLElement)||f.safari,j=/CriOS\/[\d]+/.test(navigator.userAgent);if((j||h&&i||a)&&"undefined"!=typeof FileReader){var k=new FileReader;k.onloadend=function(){var a=k.result;a=j?a:a.replace(/^data:[^;]*;/,"data:attachment/file;"),g?g.location.href=a:location=a,g=null},k.readAsDataURL(b)}else{var l=f.URL||f.webkitURL,m=l.createObjectURL(b);g?g.location=m:location.href=m,g=null,setTimeout(function(){l.revokeObjectURL(m)},4E4)}});f.saveAs=g.saveAs=g,"undefined"!=typeof module&&(module.exports=g)});
              function createPlaylist(indexSite,id,playlistName = "Playlist",duration=60){
                var pd = ""
                name = playlistName
                pd += '#EXTM3U\n'
                pd += `#EXTINF: ${duration*60} | ${name}\n`
                pd += `${indexSite}/${id}/v.mp4\n`
              return pd
              }

              function playlist(id, name) {
                hostUrl = 'https://' + window.location.host
                playlistData = createPlaylist(hostUrl,id,name);
                var blob = new Blob([playlistData],{endings: "native"});
                saveAs(blob,`${name}.m3u`);
              }
            </script>  

                <div class="mx-auto my-3 w-half px-4">
                    <div class="block md:flex md:flex-wrap p-2 md:justify-center text-center break-all">
                    {% for item in item_list %}
                        <div title="{% if item.media %} {{item.mime_type}} | {{item.human_size}} {% else %} Text message {% endif %}"  class="flex space-y-1 flex-col items-center justify-center w-full min-h-full md:w-1/5 lg:w-1/6 rounded my-4 md:mx-1 shadow-md hover:shadow-lg hover:bg-teal-600 hover:border hover:border-blue-200">
                        
                            {% if item.media %}
                                <div class="bg-blue-500 rounded font-semibold text-white py-1 px-2">{{item.file_id}}</div>

                                <a href="{{item.url}}"><img src="{{item.thumbnail}}" class="w-full rounded"></a>

                                <a href="{{item.download}}" class="bg-gray-100 hover:bg-gray-400 text-gray-900 font-semibold py-2 px-4 rounded inline-flex items-center"> <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M13 8V2H7v6H2l8 8 8-8h-5zM0 18h20v2H0v-2z"/></svg> 
                                <span>{{item.insight}}</span>
                                </a>

                                <span class="">
                                <!-- <a href="{{item.dl}}" class="bg-blue-400 hover:bg-blue-400 text-white font-bold py-1 px-4 border-b-4 border-blue-500 hover:border-blue-500 rounded"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 5 5"><path d="M13 8V2H7v6H2l8 8 8-8h-5zM0 18h20v2H0v-2z"/></svg> </a> -->

                                <a href="{{item.dl}}" class="bg-pink-400 hover:bg-blue-400 text-white py-1 px-4 border-b-4 border-pink-500 hover:border-blue-900 rounded">DL</a>

                                <a href="{{item.vlc}}" class="bg-orange-400 hover:bg-blue-400 text-white font-sans py-1 px-2 border-b-4 border-orange-500 hover:border-blue-500 rounded">VLC</a>

                                <button class="bg-green-400 hover:bg-blue-400 text-white font-sans py-1 px-2 border-b-4 border-green-500 hover:border-blue-500 rounded" onclick='playlist({{item.file_id}}, "{{item.insight}}")'>M3U</button>

                                </span>
                               
                            {% else %}
                                <div class="p-4 rounded">{{item.insight}}</div>
                            {% endif %}
                            
                        </div>
                        
                        
                    {% endfor %}
                    </div>
            
                </div>

                <p class="my-2 text-center font-semibold">
                    {{item_list|length}} items
                </p>
                
                <div class="mx-auto my-2 text-center flex text-white content-center justify-center">
                    {% if prev_page %}
                        <a title="Previous page" class="mx-2 p-2 border rounded bg-green-500 hover:border-green-500 hover:text-green-500 hover:bg-white" href="{{prev_page.url}}">Page {{prev_page.no}}</a>
                    {% endif %}
                    <p class="mx-2 p-2 border rounded border-green-500 text-green-500 hover:border-green-500 hover:text-green-500 hover:bg-white">Page {{cur_page}}</p>
                    {% if next_page %}
                        <a title="Next page" class="mx-2 p-2 border rounded bg-green-500 hover:border-green-500 hover:text-green-500 hover:bg-white" href="{{next_page.url}}">Page {{next_page.no}}</a>
                    {% endif %}
                </div>
                
            {% else %}
                
                <p class="my-4 text-center text-2xl md:text-3xl lg:text-4xl xl:text-5xl">
                    No message to display!
                </p>
            
            {% endif %}

{% include 'footer.html' %}
