<!doctype html>
<html lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="/tailwind.config.js"></script>
        <link href="https://unpkg.com/tailwindcss@^1.0/dist/tailwind.min.css" rel="stylesheet">
        
        <script src="https://cdn.fluidplayer.com/v3/current/fluidplayer.min.js"></script>
        
        <title>
            {% if title %} {{title}} {% else %} Telegram Index {% endif %}
        </title>

    </head>
    <body class="bg-gray-300" >

        <div class="m-auto w-full xl:max-w-screen-xl min-h-screen">

            <header class="flex bg-green-600 font-sans justify-between  text-white mb-2 p-4 w-full sticky top-0 shadow">

                <a id="host" href="/" class="text-left text-xl lg:text-2xl xl:text-3xl"></a>
                <script> 
                  document.getElementById("host").innerHTML = window.location.host
                </script> 

                <a href="/pc" class="bg-transparent hover:bg-blue-500 text-white font-semibold hover:text-white py-2 px-4 border border-white hover:border-transparent rounded">Playlist Creator</a>
                {% if otg %}
                    <a title="On-The-Go Indexing" href="/otg" class="text-xl lg:text-2xl xl:text-3xl"> OTG Indexing </a>
                {% endif %}
            </header>
