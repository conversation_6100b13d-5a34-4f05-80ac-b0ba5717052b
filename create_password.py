#!/usr/bin/env python3
"""
Password hashing utility for TgIndex Pro
Creates hashed passwords for the authentication system
"""

import hashlib
import getpass
import json
import sys
from pathlib import Path


def hash_password(password: str, salt: str = "tgindex_secure_salt_2024") -> str:
    """Hash password with salt"""
    return hashlib.sha256((password + salt).encode()).hexdigest()


def main():
    print("TgIndex Pro - Password Setup Utility")
    print("=" * 40)
    
    # Get username
    username = input("Enter username: ").strip()
    if not username:
        print("Username cannot be empty!")
        return 1
    
    # Get password
    password = getpass.getpass("Enter password: ").strip()
    if not password:
        print("Password cannot be empty!")
        return 1
    
    # Confirm password
    password_confirm = getpass.getpass("Confirm password: ").strip()
    if password != password_confirm:
        print("Passwords do not match!")
        return 1
    
    # Hash password
    salt = "tgindex_secure_salt_2024"
    hashed_password = hash_password(password, salt)
    
    print(f"\n✅ Password hashed successfully!")
    print(f"Username: {username}")
    print(f"Hashed Password: {hashed_password}")
    
    # Update config file
    config_file = Path("config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # Update auth section
            if "auth" not in config:
                config["auth"] = {}
            
            config["auth"]["enabled"] = True
            config["auth"]["salt"] = salt
            
            if "users" not in config["auth"]:
                config["auth"]["users"] = {}
            
            config["auth"]["users"][username] = hashed_password
            
            # Backup original config
            backup_file = config_file.with_suffix('.json.backup')
            config_file.rename(backup_file)
            print(f"📁 Original config backed up to: {backup_file}")
            
            # Save updated config
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            print(f"📝 Updated config file: {config_file}")
            print("\n🔐 Authentication is now ENABLED!")
            print(f"   Username: {username}")
            print("   You can now start the application and login with these credentials.")
            
        except Exception as e:
            print(f"❌ Error updating config file: {e}")
            return 1
    else:
        print(f"\n⚠️  Config file not found: {config_file}")
        print("Please add the following to your config.json:")
        print(f"""
{{
  "auth": {{
    "enabled": true,
    "salt": "{salt}",
    "users": {{
      "{username}": "{hashed_password}"
    }}
  }}
}}
""")
    
    return 0


if __name__ == "__main__":
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        exit(1)
