# 🚀 TgIndex Pro - Setup Guide

## 🆕 New Features

### 🔐 **Authentication System**
- Secure login with username/password
- Session management with encrypted cookies
- Multiple user support
- Optional authentication (can be disabled)

### 📺 **Multi-Channel Support**
- Index multiple Telegram channels/groups
- Individual channel management
- Channel-specific aliases and descriptions
- Enable/disable channels independently

### 🧹 **Clean Codebase**
- Removed unnecessary files
- Modular architecture
- Better error handling
- Improved configuration system

## 🛠️ Quick Setup

### 1. **Automated Setup (Recommended)**
```bash
python setup_tgindex_pro.py
```
This will guide you through:
- Telegram API configuration
- Authentication setup
- Multi-channel configuration
- Server settings

### 2. **Manual Password Setup**
```bash
python create_password.py
```
Use this to add/change user passwords after initial setup.

### 3. **Start the Application**
```bash
python start_app.py
```

## 📋 Configuration Structure

### Authentication
```json
{
  "auth": {
    "enabled": true,
    "salt": "your_secure_salt",
    "users": {
      "admin": "hashed_password_here",
      "user2": "another_hashed_password"
    }
  }
}
```

### Multi-Channel
```json
{
  "channels": {
    "main_channel": {
      "chat_id": -1002788057044,
      "alias_id": "movies",
      "description": "Movie Collection",
      "enabled": true
    },
    "channel_2": {
      "chat_id": -1001234567890,
      "alias_id": "music",
      "description": "Music Library",
      "enabled": true
    }
  }
}
```

## 🔑 Authentication Features

### Login System
- Beautiful login page with modern UI
- Secure session management
- Remember login for 24 hours
- Automatic logout on session expiry

### Access Control
- All pages protected when auth is enabled
- API endpoints return 401 for unauthorized access
- Graceful redirects to login page

### User Management
- Add multiple users with different passwords
- Hash passwords securely with salt
- Easy password updates

## 📺 Multi-Channel Features

### Channel Management
- Add unlimited channels/groups
- Each channel gets unique alias ID
- Individual descriptions and settings
- Enable/disable channels without removing

### Channel Access
- Access channels via: `http://localhost:8080/{alias_id}`
- Example: `http://localhost:8080/movies`
- Automatic channel detection and routing

### Channel Statistics
- Member counts
- Last message information
- Channel status and health

## 🌐 URL Structure

### Main Pages
- `/` - Home page (lists all channels if multiple)
- `/login` - Login page (if auth enabled)
- `/logout` - Logout endpoint
- `/pc` - Playlist Creator
- `/otg` - On-The-Go indexing

### Channel Pages
- `/{alias_id}` - Channel index
- `/{alias_id}/{id}/view` - File view page
- `/{alias_id}/{id}/download` - Direct download
- `/{alias_id}/{id}/thumbnail` - File thumbnail
- `/{alias_id}/{id}/m3u` - M3U playlist

### API Endpoints
- `/api/auth/status` - Authentication status
- `/debug` - Debug information

## 🔧 Environment Variables

You can also use environment variables instead of config.json:

```bash
# Telegram API
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_SESSION_STRING=your_session_string

# Server
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Authentication
AUTH_ENABLED=true
AUTH_ADMIN_USERNAME=admin
AUTH_ADMIN_PASSWORD=your_password

# Channels
CHANNEL_1_ID=-1002788057044
CHANNEL_1_ALIAS=movies
CHANNEL_1_DESCRIPTION=Movie Collection
```

## 🚀 Deployment

### Local Development
```bash
python start_app.py
```

### Production
1. Set `auth.enabled` to `true`
2. Use strong passwords
3. Configure proper `allowed_hosts`
4. Enable HTTPS if possible
5. Set `server.debug` to `false`

## 🔒 Security Features

- Password hashing with salt
- Secure session cookies
- CSRF protection ready
- Input validation
- Error handling without information leakage

## 📱 Mobile Support

- Responsive design
- Touch-friendly interface
- Mobile-optimized login page
- Progressive Web App ready

## 🎨 Customization

### Themes
- Default theme included
- Easy to customize CSS
- Template-based architecture

### Features Toggle
```json
{
  "features": {
    "download_enabled": true,
    "streaming_enabled": true,
    "search_enabled": true,
    "playlist_enabled": true,
    "multi_channel": true,
    "channel_management": true
  }
}
```

## 🐛 Troubleshooting

### Authentication Issues
- Check password hash generation
- Verify session cookie settings
- Clear browser cookies

### Channel Issues
- Verify chat IDs are correct
- Check Telegram API permissions
- Ensure bot/user has access to channels

### Performance
- Monitor concurrent downloads
- Adjust rate limits
- Check memory usage

## 📞 Support

For issues and questions:
1. Check this guide first
2. Review configuration files
3. Check application logs
4. Verify Telegram API access

---

**🎉 Enjoy your secure, multi-channel TgIndex Pro!**
