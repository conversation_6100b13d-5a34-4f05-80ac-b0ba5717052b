"""
Multi-channel management system for TgIndex Pro
Handles multiple Telegram channels/groups indexing
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from .telegram import Client
from .config import session_string, api_id, api_hash

log = logging.getLogger(__name__)


@dataclass
class ChannelInfo:
    """Information about a configured channel"""
    id: int
    title: str
    username: Optional[str]
    alias_id: str
    enabled: bool = True
    description: Optional[str] = None
    member_count: Optional[int] = None
    last_indexed: Optional[str] = None


class ChannelManager:
    """Manages multiple Telegram channels"""
    
    def __init__(self, config):
        self.config = config
        self.channels: Dict[str, ChannelInfo] = {}
        self.client = None
    
    async def initialize(self):
        """Initialize the channel manager"""
        # Create Telegram client
        self.client = Client(session_string, api_id, api_hash)
        await self.client.start()
        
        # Load configured channels
        await self.load_channels()
    
    async def load_channels(self):
        """Load channels from configuration"""
        channels_config = self.config.get("channels", {})
        
        for channel_config in channels_config.values():
            try:
                await self.add_channel_from_config(channel_config)
            except Exception as e:
                log.error(f"Failed to load channel {channel_config}: {e}")
    
    async def add_channel_from_config(self, channel_config: Dict[str, Any]):
        """Add a channel from configuration"""
        chat_id = channel_config.get("chat_id")
        if not chat_id:
            return
        
        try:
            # Get channel entity from Telegram
            entity = await self.client.get_entity(chat_id)
            
            # Generate alias ID
            alias_id = channel_config.get("alias_id") or self.generate_alias_id(entity)
            
            # Create channel info
            channel_info = ChannelInfo(
                id=entity.id,
                title=entity.title,
                username=getattr(entity, 'username', None),
                alias_id=alias_id,
                enabled=channel_config.get("enabled", True),
                description=channel_config.get("description", entity.title)
            )
            
            # Add to channels dict
            self.channels[alias_id] = channel_info
            
            log.info(f"Added channel: {channel_info.title} ({alias_id})")
            
        except Exception as e:
            log.error(f"Failed to add channel {chat_id}: {e}")
    
    def generate_alias_id(self, entity) -> str:
        """Generate a unique alias ID for a channel"""
        import random
        import string
        
        # Use channel username if available, otherwise generate random
        if hasattr(entity, 'username') and entity.username:
            base = entity.username.lower()
        else:
            base = ''.join(random.choices(string.ascii_lowercase, k=8))
        
        # Ensure uniqueness
        alias_id = base
        counter = 1
        while alias_id in self.channels:
            alias_id = f"{base}{counter}"
            counter += 1
        
        return alias_id
    
    async def add_channel(self, chat_id: int, alias_id: Optional[str] = None, 
                         description: Optional[str] = None) -> ChannelInfo:
        """Add a new channel dynamically"""
        try:
            # Get channel entity
            entity = await self.client.get_entity(chat_id)
            
            # Generate alias if not provided
            if not alias_id:
                alias_id = self.generate_alias_id(entity)
            
            # Create channel info
            channel_info = ChannelInfo(
                id=entity.id,
                title=entity.title,
                username=getattr(entity, 'username', None),
                alias_id=alias_id,
                enabled=True,
                description=description or entity.title
            )
            
            # Add to channels
            self.channels[alias_id] = channel_info
            
            # Update configuration
            await self.save_channel_to_config(channel_info)
            
            log.info(f"Added new channel: {channel_info.title} ({alias_id})")
            return channel_info
            
        except Exception as e:
            log.error(f"Failed to add channel {chat_id}: {e}")
            raise
    
    async def remove_channel(self, alias_id: str) -> bool:
        """Remove a channel"""
        if alias_id in self.channels:
            channel_info = self.channels[alias_id]
            del self.channels[alias_id]
            
            # Update configuration
            await self.remove_channel_from_config(alias_id)
            
            log.info(f"Removed channel: {channel_info.title} ({alias_id})")
            return True
        return False
    
    async def toggle_channel(self, alias_id: str) -> bool:
        """Enable/disable a channel"""
        if alias_id in self.channels:
            self.channels[alias_id].enabled = not self.channels[alias_id].enabled
            await self.save_channel_to_config(self.channels[alias_id])
            return True
        return False
    
    def get_channel(self, alias_id: str) -> Optional[ChannelInfo]:
        """Get channel info by alias ID"""
        return self.channels.get(alias_id)
    
    def get_enabled_channels(self) -> List[ChannelInfo]:
        """Get all enabled channels"""
        return [ch for ch in self.channels.values() if ch.enabled]
    
    def get_all_channels(self) -> List[ChannelInfo]:
        """Get all channels"""
        return list(self.channels.values())
    
    async def save_channel_to_config(self, channel_info: ChannelInfo):
        """Save channel to configuration file"""
        # This would update the config file
        # For now, we'll just log it
        log.info(f"Would save channel {channel_info.alias_id} to config")
    
    async def remove_channel_from_config(self, alias_id: str):
        """Remove channel from configuration file"""
        # This would update the config file
        # For now, we'll just log it
        log.info(f"Would remove channel {alias_id} from config")
    
    async def get_channel_stats(self, alias_id: str) -> Dict[str, Any]:
        """Get statistics for a channel"""
        channel_info = self.get_channel(alias_id)
        if not channel_info:
            return {}
        
        try:
            # Get recent messages count
            messages = await self.client.get_messages(channel_info.id, limit=1)
            
            # Get channel entity for member count
            entity = await self.client.get_entity(channel_info.id)
            
            return {
                'title': channel_info.title,
                'member_count': getattr(entity, 'participants_count', 0),
                'last_message_id': messages[0].id if messages else 0,
                'enabled': channel_info.enabled
            }
        except Exception as e:
            log.error(f"Failed to get stats for {alias_id}: {e}")
            return {}
    
    async def disconnect(self):
        """Disconnect the Telegram client"""
        if self.client:
            await self.client.disconnect()


# Global channel manager instance
channel_manager = None


async def get_channel_manager(config) -> ChannelManager:
    """Get or create the global channel manager"""
    global channel_manager
    if channel_manager is None:
        channel_manager = ChannelManager(config)
        await channel_manager.initialize()
    return channel_manager
