#!/usr/bin/env python3
"""
Test script to check chat access and messages
"""

import asyncio
import os
from telethon import TelegramClient
from telethon.sessions import StringSession

async def test_chat():
    # Set environment variables
    os.environ["API_ID"] = "24522627"
    os.environ["API_HASH"] = "d20c1537e9dc685ddcdb66aac7442cce"
    os.environ["SESSION_STRING"] = "1BVtsOJUBuxj1GLvqA_KjSu5_Mga9H1t_eCehPzPmKpHxGCNs0XeGjVF3WjnflWbsosQIIrf9BqtCsnrNSWVxvOyOaQrUpSHz4Smm1qZZdQEWQ3wAOCAT4FeG-PeeHandVgG21ZJN02L71LjLapc-31REL8HeOLty2aheamfutPLLgI4sCVNDjKUQTptM90tOte3FFzpvcdma0GYEPalLi3LOAYT4hP28RPqYLIvhbpyxK3D1FySNaYey9XYVPn7Xq6dhfcApb81QaNcri12ZEvpUcdhRl3p-HiYzpWPyvbQRYa_NIQZEl5lN3nroAc8WsfvM8UwBJKCbjLt4Uifh0XXcQr3_IxM="
    
    api_id = int(os.environ["API_ID"])
    api_hash = os.environ["API_HASH"]
    session_string = os.environ["SESSION_STRING"]
    chat_id = -1002788057044
    
    print(f"🔄 Testing chat access for chat_id: {chat_id}")
    
    client = TelegramClient(StringSession(session_string), api_id, api_hash)
    
    try:
        await client.connect()
        print("✅ Connected to Telegram")
        
        if not await client.is_user_authorized():
            print("❌ Not authorized")
            return
        
        me = await client.get_me()
        print(f"✅ Logged in as: {me.first_name} (@{me.username})")
        
        # Test getting the chat entity
        try:
            chat = await client.get_entity(chat_id)
            print(f"✅ Chat found: {chat.title}")
            print(f"   Type: {type(chat).__name__}")
            print(f"   ID: {chat.id}")
            if hasattr(chat, 'username'):
                print(f"   Username: @{chat.username}")
        except Exception as e:
            print(f"❌ Failed to get chat entity: {e}")
            return
        
        # Test getting messages
        try:
            print(f"🔄 Getting recent messages...")
            messages = await client.get_messages(chat_id, limit=10)
            print(f"✅ Found {len(messages)} messages")
            
            for i, msg in enumerate(messages[:5]):
                print(f"   Message {i+1}:")
                print(f"     ID: {msg.id}")
                print(f"     Date: {msg.date}")
                if msg.message:
                    print(f"     Text: {msg.message[:50]}...")
                if msg.file:
                    print(f"     File: {msg.file.name or 'Unnamed'} ({msg.file.size} bytes)")
                print()
                
        except Exception as e:
            print(f"❌ Failed to get messages: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.disconnect()
        print("🔌 Disconnected")

if __name__ == "__main__":
    asyncio.run(test_chat())
