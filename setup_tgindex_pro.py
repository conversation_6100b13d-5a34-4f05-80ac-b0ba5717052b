#!/usr/bin/env python3
"""
TgIndex Pro Setup Script
Complete setup for authentication, multi-channel support, and configuration
"""

import json
import hashlib
import getpass
import shutil
from pathlib import Path


def hash_password(password: str, salt: str = "tgindex_secure_salt_2024") -> str:
    """Hash password with salt"""
    return hashlib.sha256((password + salt).encode()).hexdigest()


def setup_authentication():
    """Setup authentication system"""
    print("\n🔐 AUTHENTICATION SETUP")
    print("=" * 40)
    
    enable_auth = input("Enable authentication? (y/n) [n]: ").lower().strip()
    if enable_auth not in ['y', 'yes']:
        return {"enabled": False}
    
    print("Setting up admin user...")
    username = input("Admin username [admin]: ").strip() or "admin"
    
    while True:
        password = getpass.getpass("Admin password: ").strip()
        if not password:
            print("Password cannot be empty!")
            continue
        
        password_confirm = getpass.getpass("Confirm password: ").strip()
        if password != password_confirm:
            print("Passwords do not match!")
            continue
        break
    
    salt = "tgindex_secure_salt_2024"
    hashed_password = hash_password(password, salt)
    
    return {
        "enabled": True,
        "salt": salt,
        "users": {
            username: hashed_password
        }
    }


def setup_channels():
    """Setup multiple channels"""
    print("\n📺 CHANNEL SETUP")
    print("=" * 40)
    
    channels = {}
    
    # Main channel (required)
    print("Setting up main channel...")
    main_chat_id = input("Main channel/group chat ID (e.g., -1002788057044): ").strip()
    if not main_chat_id:
        print("Main channel is required!")
        return None
    
    try:
        main_chat_id = int(main_chat_id)
    except ValueError:
        print("Invalid chat ID format!")
        return None
    
    main_alias = input("Main channel alias [tgindex]: ").strip() or "tgindex"
    main_description = input("Main channel description [Main Channel]: ").strip() or "Main Channel"
    
    channels["main_channel"] = {
        "chat_id": main_chat_id,
        "alias_id": main_alias,
        "description": main_description,
        "enabled": True
    }
    
    # Additional channels
    while True:
        add_more = input("\nAdd another channel? (y/n) [n]: ").lower().strip()
        if add_more not in ['y', 'yes']:
            break
        
        chat_id = input("Channel/group chat ID: ").strip()
        if not chat_id:
            continue
        
        try:
            chat_id = int(chat_id)
        except ValueError:
            print("Invalid chat ID format!")
            continue
        
        alias = input("Channel alias: ").strip()
        if not alias:
            print("Alias is required!")
            continue
        
        description = input(f"Channel description [{alias}]: ").strip() or alias
        
        channel_key = f"channel_{len(channels)}"
        channels[channel_key] = {
            "chat_id": chat_id,
            "alias_id": alias,
            "description": description,
            "enabled": True
        }
        
        print(f"✅ Added channel: {description} ({alias})")
    
    return channels


def create_config():
    """Create complete configuration"""
    print("🚀 TgIndex Pro Setup")
    print("=" * 40)
    
    # Check if config exists
    config_file = Path("config.json")
    if config_file.exists():
        backup = input("Config file exists. Create backup? (y/n) [y]: ").lower().strip()
        if backup != 'n':
            backup_file = config_file.with_suffix('.json.backup')
            shutil.copy2(config_file, backup_file)
            print(f"📁 Backup created: {backup_file}")
    
    # Load existing config or create new
    if config_file.exists():
        with open(config_file, 'r') as f:
            config = json.load(f)
    else:
        config = {}
    
    # Telegram settings
    print("\n📱 TELEGRAM API SETTINGS")
    print("Get your API credentials from https://my.telegram.org/apps")
    
    api_id = input(f"API ID [{config.get('telegram', {}).get('api_id', '')}]: ").strip()
    if api_id:
        try:
            api_id = int(api_id)
        except ValueError:
            print("Invalid API ID!")
            return False
    else:
        api_id = config.get('telegram', {}).get('api_id')
    
    api_hash = input(f"API Hash [{config.get('telegram', {}).get('api_hash', '')}]: ").strip()
    if not api_hash:
        api_hash = config.get('telegram', {}).get('api_hash')
    
    session_string = input("Session String (run generate_session_string.py if needed): ").strip()
    if not session_string:
        session_string = config.get('telegram', {}).get('session_string')
    
    if not all([api_id, api_hash, session_string]):
        print("❌ Telegram API settings are required!")
        return False
    
    # Setup authentication
    auth_config = setup_authentication()
    
    # Setup channels
    channels_config = setup_channels()
    if not channels_config:
        print("❌ At least one channel is required!")
        return False
    
    # Server settings
    print("\n🌐 SERVER SETTINGS")
    host = input("Host [0.0.0.0]: ").strip() or "0.0.0.0"
    port = input("Port [8080]: ").strip() or "8080"
    try:
        port = int(port)
    except ValueError:
        port = 8080
    
    # Create final config
    final_config = {
        "telegram": {
            "api_id": api_id,
            "api_hash": api_hash,
            "session_string": session_string,
            "bot_token": None
        },
        "server": {
            "host": host,
            "port": port,
            "debug": False
        },
        "auth": auth_config,
        "channels": channels_config,
        "indexing": {
            "settings": {
                "index_all": False,
                "index_private": True,
                "index_group": True,
                "index_channel": True,
                "exclude_chats": [],
                "otg": {
                    "enable": True,
                    "include_private": True,
                    "include_group": True,
                    "include_channel": True
                }
            }
        },
        "ui": {
            "site_title": "TgIndex Pro",
            "site_description": "Secure Telegram Media Index",
            "theme": "default",
            "items_per_page": 20,
            "show_channel_stats": True,
            "show_logout_button": auth_config["enabled"]
        },
        "logging": {
            "level": "INFO",
            "telegram_level": "ERROR",
            "aiohttp_level": "ERROR"
        },
        "security": {
            "allowed_hosts": [],
            "cors_enabled": False,
            "cors_origins": ["*"]
        },
        "features": {
            "download_enabled": True,
            "streaming_enabled": True,
            "search_enabled": True,
            "playlist_enabled": True,
            "multi_channel": True,
            "channel_management": True
        },
        "limits": {
            "max_file_size_mb": 2048,
            "max_concurrent_downloads": 5,
            "rate_limit_requests_per_minute": 60
        }
    }
    
    # Save config
    with open(config_file, 'w') as f:
        json.dump(final_config, f, indent=2)
    
    print(f"\n✅ Configuration saved to {config_file}")
    
    # Summary
    print("\n📋 SETUP SUMMARY")
    print("=" * 40)
    print(f"🔐 Authentication: {'Enabled' if auth_config['enabled'] else 'Disabled'}")
    print(f"📺 Channels configured: {len(channels_config)}")
    print(f"🌐 Server: {host}:{port}")
    
    if auth_config["enabled"]:
        print(f"\n🔑 Login credentials:")
        for username in auth_config["users"]:
            print(f"   Username: {username}")
        print("   Password: (as entered)")
    
    print(f"\n🚀 To start the application:")
    print("   python start_app.py")
    
    return True


def main():
    """Main setup function"""
    try:
        success = create_config()
        if success:
            print("\n🎉 Setup completed successfully!")
            print("Your TgIndex Pro is ready to use!")
        else:
            print("\n❌ Setup failed!")
            return 1
    except KeyboardInterrupt:
        print("\n\n⚠️ Setup cancelled by user.")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
