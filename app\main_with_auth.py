import asyncio
import pathlib
import logging
import base64
import os

import aiohttp_jinja2
import jinja2
from aiohttp import web
from aiohttp_session import setup as setup_session
from aiohttp_session.cookie_storage import EncryptedCookieStorage
from cryptography.fernet import Fernet

from .telegram import Client
from .routes import setup_routes
from .views import Views
from .config import host, port, session_string, api_id, api_hash, debug
from .auth import AuthManager, AuthViews, setup_auth_routes
from .channel_manager import get_channel_manager


log = logging.getLogger(__name__)


class Indexer:

    TEMPLATES_ROOT = pathlib.Path(__file__).parent / 'templates'

    def __init__(self):
        self.server = web.Application()
        self.loop = asyncio.get_event_loop()
        self.tg_client = None  # Initialize later in startup
        self.auth_manager = None
        self.channel_manager = None


    async def startup(self):
        # Import config manager
        from .config_manager import config
        
        # Setup session storage for authentication with proper key
        secret_key = base64.urlsafe_b64encode(os.urandom(32))
        setup_session(self.server, EncryptedCookieStorage(secret_key))
        
        # Initialize authentication
        self.auth_manager = AuthManager(config)
        auth_views = AuthViews(self.auth_manager, config)
        setup_auth_routes(self.server, auth_views)
        
        # Initialize channel manager
        self.channel_manager = await get_channel_manager(config)
        
        # Create and start the Telegram client in the same event loop
        self.tg_client = Client(session_string, api_id, api_hash)
        await self.tg_client.start()
        log.debug("telegram client started!")

        # Setup views with authentication and channel manager
        views = Views(self.tg_client, self.auth_manager, self.channel_manager, config)
        await setup_routes(self.server, views)

        # Setup Jinja2 templates
        loader = jinja2.FileSystemLoader(str(self.TEMPLATES_ROOT))
        aiohttp_jinja2.setup(self.server, loader=loader)

        self.server.on_cleanup.append(self.cleanup)


    async def cleanup(self, *args):
        if self.tg_client:
            await self.tg_client.disconnect()
            log.debug("telegram client disconnected!")
        
        if self.channel_manager:
            await self.channel_manager.disconnect()
            log.debug("channel manager disconnected!")
        
        if self.auth_manager:
            self.auth_manager.cleanup_expired_sessions()
            log.debug("auth manager cleaned up!")


    def run(self):
        self.loop.run_until_complete(self.startup())
        web.run_app(
            self.server,
            host=host,
            port=port,
            loop=self.loop
        )
