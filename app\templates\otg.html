{% include 'header.html' %}

    <h1 class="px-4 my-2 text-2xl text-center font-bold text-green-400">
        On-The-Go Indexing
    </h1>

    <div class="px-4 mx-auto my-2">

        <p class="text-center text-gray-700">
            On-The-Go (OTG) Indexing. Index any public telegram channel or public telegram group on the go with it's username.
        </p>

    </div>

    {% if error %}
        <div id="alert" class="px-4 mx-auto my-2 text-center m-2 ">
            <p class="text-white bg-red-500 w-auto inline-block px-4 py-2 rounded-lg"> {{error}} </p>
        </div>
        <script>
        setTimeout(function(){
            document.getElementById("alert").style.display = 'none';
        }, 7000);
        </script>
    {% endif %}

    <div class="px-4 mx-auto my-2">
        <form class="my-2 text-center flex justify-center" action="/otg" method="POST">
                <input class="px-4 py-2 rounded-l-full border border-r-0 border-teal-400 outline-none" type="text" name="id" placeholder="Enter username..." required>
                <button class="px-4 py-2 bg-teal-400 border border-teal-400 hover:bg-white hover:text-teal-400 text-white rounded-r-full outline-none" type="submit">Get Result</button>
        </form>
    </div>


{% include 'footer.html' %}
