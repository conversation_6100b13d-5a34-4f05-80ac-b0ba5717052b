# TgIndex Pro Startup Script
Write-Host "Setting up TgIndex Pro..." -ForegroundColor Green

# Set environment variables
$env:API_ID = "24522627"
$env:API_HASH = "d20c1537e9dc685ddcdb66aac7442cce"
$env:SESSION_STRING = "1BVtsOJUBuxj1GLvqA_KjSu5_Mga9H1t_eCehPzPmKpHxGCNs0XeGjVF3WjnflWbsosQIIrf9BqtCsnrNSWVxvOyOaQrUpSHz4Smm1qZZdQEWQ3wAOCAT4FeG-PeeHandVgG21ZJN02L71LjLapc-31REL8HeOLty2aheamfutPLLgI4sCVNDjKUQTptM90tOte3FFzpvcdma0GYEPalLi3LOAYT4hP28RPqYLIvhbpyxK3D1FySNaYey9XYVPn7Xq6dhfcApb81QaNcri12ZEvpUcdhRl3p-HiYzpWPyvbQRYa_NIQZEl5lN3nroAc8WsfvM8UwBJKCbjLt4Uifh0XXcQr3_IxM="
$env:INDEXING_CHAT = "-1002788057044"
$env:HOST = "0.0.0.0"
$env:PORT = "8080"
$env:DEBUG = "false"

Write-Host "Environment variables set successfully!" -ForegroundColor Green
Write-Host "Starting TgIndex Pro..." -ForegroundColor Yellow

# Run the application
python -m app
