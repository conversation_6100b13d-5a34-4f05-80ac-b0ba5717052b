#!/usr/bin/env python3
"""
Quick fix to get TgIndex Pro working with new config structure
"""

import json
from pathlib import Path


def fix_config():
    """Fix the config to work with current system"""
    config_file = Path("config.json")
    
    if not config_file.exists():
        print("❌ config.json not found!")
        return False
    
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Extract channel info and add to indexing section for compatibility
    channels = config.get("channels", {})
    if channels:
        # Get the first channel
        first_channel = list(channels.values())[0]
        chat_id = first_channel.get("chat_id")
        
        if chat_id:
            # Update indexing section for backward compatibility
            if "indexing" not in config:
                config["indexing"] = {}
            
            config["indexing"]["chat_id"] = chat_id
            
            if "settings" not in config["indexing"]:
                config["indexing"]["settings"] = {}
            
            config["indexing"]["settings"]["include_chats"] = [chat_id]
            
            print(f"✅ Fixed config for chat_id: {chat_id}")
    
    # Temporarily disable auth for testing
    if "auth" in config:
        config["auth"]["enabled"] = False
        print("🔐 Temporarily disabled authentication for testing")
    
    # Save fixed config
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Config fixed successfully!")
    return True


if __name__ == "__main__":
    fix_config()
