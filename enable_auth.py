#!/usr/bin/env python3
"""
Enable authentication for TgIndex Pro
Fixes the session key issue and enables secure login
"""

import json
from pathlib import Path


def enable_authentication():
    """Enable authentication in config"""
    config_file = Path("config.json")
    
    if not config_file.exists():
        print("❌ config.json not found!")
        return False
    
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Enable authentication
    if "auth" in config:
        config["auth"]["enabled"] = True
        print("🔐 Authentication enabled in config")
    else:
        print("❌ No auth section found in config")
        return False
    
    # Save config
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Authentication enabled successfully!")
    print("🔑 You can now login with:")
    print("   Username: admin")
    print("   Password: (as set with create_password.py)")
    
    return True


def main():
    print("🔐 TgIndex Pro - Enable Authentication")
    print("=" * 40)
    
    # Check if password is set
    config_file = Path("config.json")
    if config_file.exists():
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        auth_config = config.get("auth", {})
        users = auth_config.get("users", {})
        
        if not users:
            print("⚠️  No users configured!")
            print("   Run: python create_password.py")
            return 1
        
        print(f"👥 Found {len(users)} user(s): {list(users.keys())}")
    
    success = enable_authentication()
    
    if success:
        print("\n🚀 Next steps:")
        print("1. Restart the application: python start_app.py")
        print("2. Visit: http://localhost:8080")
        print("3. You'll be redirected to login page")
        print("4. Login with your credentials")
        print("\n🔒 Your TgIndex Pro is now secure!")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
