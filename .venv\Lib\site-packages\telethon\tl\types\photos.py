"""File generated by TLObjects' generator. All changes will be ERASED"""
from ...tl.tlobject import TLObject
from typing import Optional, List, Union, TYPE_CHECKING
import os
import struct
from datetime import datetime
if TYPE_CHECKING:
    from ...tl.types import TypePhoto, TypeUser



class Photo(TLObject):
    CONSTRUCTOR_ID = 0x20212ca8
    SUBCLASS_OF_ID = 0xc292bd24

    def __init__(self, photo: 'TypePhoto', users: List['TypeUser']):
        """
        Constructor for photos.Photo: Instance of Photo.
        """
        self.photo = photo
        self.users = users

    def to_dict(self):
        return {
            '_': 'Photo',
            'photo': self.photo.to_dict() if isinstance(self.photo, TLObject) else self.photo,
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'\xa8,! ',
            self.photo._bytes(),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        _photo = reader.tgread_object()
        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(photo=_photo, users=_users)


class Photos(TLObject):
    CONSTRUCTOR_ID = 0x8dca6aa5
    SUBCLASS_OF_ID = 0x27cfb967

    def __init__(self, photos: List['TypePhoto'], users: List['TypeUser']):
        """
        Constructor for photos.Photos: Instance of either Photos, PhotosSlice.
        """
        self.photos = photos
        self.users = users

    def to_dict(self):
        return {
            '_': 'Photos',
            'photos': [] if self.photos is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.photos],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'\xa5j\xca\x8d',
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.photos)),b''.join(x._bytes() for x in self.photos),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        reader.read_int()
        _photos = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _photos.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(photos=_photos, users=_users)


class PhotosSlice(TLObject):
    CONSTRUCTOR_ID = 0x15051f54
    SUBCLASS_OF_ID = 0x27cfb967

    def __init__(self, count: int, photos: List['TypePhoto'], users: List['TypeUser']):
        """
        Constructor for photos.Photos: Instance of either Photos, PhotosSlice.
        """
        self.count = count
        self.photos = photos
        self.users = users

    def to_dict(self):
        return {
            '_': 'PhotosSlice',
            'count': self.count,
            'photos': [] if self.photos is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.photos],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'T\x1f\x05\x15',
            struct.pack('<i', self.count),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.photos)),b''.join(x._bytes() for x in self.photos),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        _count = reader.read_int()
        reader.read_int()
        _photos = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _photos.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(count=_count, photos=_photos, users=_users)

