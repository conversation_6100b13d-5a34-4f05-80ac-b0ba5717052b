"""File generated by TLObjects' generator. All changes will be ERASED"""
from ...tl.tlobject import TLObject
from typing import Optional, List, Union, TYPE_CHECKING
import os
import struct
from datetime import datetime


class CollectibleInfo(TLObject):
    CONSTRUCTOR_ID = 0x6ebdff91
    SUBCLASS_OF_ID = 0xd4ea5790

    def __init__(self, purchase_date: Optional[datetime], currency: str, amount: int, crypto_currency: str, crypto_amount: int, url: str):
        """
        Constructor for fragment.CollectibleInfo: Instance of CollectibleInfo.
        """
        self.purchase_date = purchase_date
        self.currency = currency
        self.amount = amount
        self.crypto_currency = crypto_currency
        self.crypto_amount = crypto_amount
        self.url = url

    def to_dict(self):
        return {
            '_': 'CollectibleInfo',
            'purchase_date': self.purchase_date,
            'currency': self.currency,
            'amount': self.amount,
            'crypto_currency': self.crypto_currency,
            'crypto_amount': self.crypto_amount,
            'url': self.url
        }

    def _bytes(self):
        return b''.join((
            b'\x91\xff\xbdn',
            self.serialize_datetime(self.purchase_date),
            self.serialize_bytes(self.currency),
            struct.pack('<q', self.amount),
            self.serialize_bytes(self.crypto_currency),
            struct.pack('<q', self.crypto_amount),
            self.serialize_bytes(self.url),
        ))

    @classmethod
    def from_reader(cls, reader):
        _purchase_date = reader.tgread_date()
        _currency = reader.tgread_string()
        _amount = reader.read_long()
        _crypto_currency = reader.tgread_string()
        _crypto_amount = reader.read_long()
        _url = reader.tgread_string()
        return cls(purchase_date=_purchase_date, currency=_currency, amount=_amount, crypto_currency=_crypto_currency, crypto_amount=_crypto_amount, url=_url)

