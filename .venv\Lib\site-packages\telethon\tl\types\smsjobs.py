"""File generated by TLObjects' generator. All changes will be ERASED"""
from ...tl.tlobject import TLObject
from typing import Optional, List, Union, TYPE_CHECKING
import os
import struct
from datetime import datetime


class EligibleToJoin(TLObject):
    CONSTRUCTOR_ID = 0xdc8b44cf
    SUBCLASS_OF_ID = 0x5eb760a6

    def __init__(self, terms_url: str, monthly_sent_sms: int):
        """
        Constructor for smsjobs.EligibilityToJoin: Instance of EligibleToJoin.
        """
        self.terms_url = terms_url
        self.monthly_sent_sms = monthly_sent_sms

    def to_dict(self):
        return {
            '_': 'EligibleToJoin',
            'terms_url': self.terms_url,
            'monthly_sent_sms': self.monthly_sent_sms
        }

    def _bytes(self):
        return b''.join((
            b'\xcfD\x8b\xdc',
            self.serialize_bytes(self.terms_url),
            struct.pack('<i', self.monthly_sent_sms),
        ))

    @classmethod
    def from_reader(cls, reader):
        _terms_url = reader.tgread_string()
        _monthly_sent_sms = reader.read_int()
        return cls(terms_url=_terms_url, monthly_sent_sms=_monthly_sent_sms)


class Status(TLObject):
    CONSTRUCTOR_ID = 0x2aee9191
    SUBCLASS_OF_ID = 0xcd8f2b25

    def __init__(self, recent_sent: int, recent_since: Optional[datetime], recent_remains: int, total_sent: int, total_since: Optional[datetime], terms_url: str, allow_international: Optional[bool]=None, last_gift_slug: Optional[str]=None):
        """
        Constructor for smsjobs.Status: Instance of Status.
        """
        self.recent_sent = recent_sent
        self.recent_since = recent_since
        self.recent_remains = recent_remains
        self.total_sent = total_sent
        self.total_since = total_since
        self.terms_url = terms_url
        self.allow_international = allow_international
        self.last_gift_slug = last_gift_slug

    def to_dict(self):
        return {
            '_': 'Status',
            'recent_sent': self.recent_sent,
            'recent_since': self.recent_since,
            'recent_remains': self.recent_remains,
            'total_sent': self.total_sent,
            'total_since': self.total_since,
            'terms_url': self.terms_url,
            'allow_international': self.allow_international,
            'last_gift_slug': self.last_gift_slug
        }

    def _bytes(self):
        return b''.join((
            b'\x91\x91\xee*',
            struct.pack('<I', (0 if self.allow_international is None or self.allow_international is False else 1) | (0 if self.last_gift_slug is None or self.last_gift_slug is False else 2)),
            struct.pack('<i', self.recent_sent),
            self.serialize_datetime(self.recent_since),
            struct.pack('<i', self.recent_remains),
            struct.pack('<i', self.total_sent),
            self.serialize_datetime(self.total_since),
            b'' if self.last_gift_slug is None or self.last_gift_slug is False else (self.serialize_bytes(self.last_gift_slug)),
            self.serialize_bytes(self.terms_url),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _allow_international = bool(flags & 1)
        _recent_sent = reader.read_int()
        _recent_since = reader.tgread_date()
        _recent_remains = reader.read_int()
        _total_sent = reader.read_int()
        _total_since = reader.tgread_date()
        if flags & 2:
            _last_gift_slug = reader.tgread_string()
        else:
            _last_gift_slug = None
        _terms_url = reader.tgread_string()
        return cls(recent_sent=_recent_sent, recent_since=_recent_since, recent_remains=_recent_remains, total_sent=_total_sent, total_since=_total_since, terms_url=_terms_url, allow_international=_allow_international, last_gift_slug=_last_gift_slug)

