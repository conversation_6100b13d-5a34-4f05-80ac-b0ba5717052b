<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>@import url(https://fonts.googleapis.com/css?family=Nunito+Sans:400,300,600,400italic);
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-font-smoothing: antialiased;
  -o-font-smoothing: antialiased;
  font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: "Nunito Sans", Helvetica, Arial, sans-serif;
}

body {
  font-weight: 100;
  font-size: 12px;
  line-height: 30px;
  color: #777;
  background: #e2e8f0;
}

.container {
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
  position: relative;
}

#contact input[type="text"],
#contact input[type="email"],
#contact input[type="tel"],
#contact input[type="url"],
#contact textarea,
#contact button[type="submit"] {
  font: 400 12px/16px;
}

#contact {
  background: #F9F9F9;
  padding: 25px;
  margin: 150px 0;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
}

#contact h3 {
  display: block;
  font-size: 30px;
  font-weight: 300;
  margin-bottom: 10px;
}

#contact h4 {
  margin: 5px 0 15px;
  display: block;
  font-size: 13px;
  font-weight: 400;
}

fieldset {
  border: medium none !important;
  margin: 0 0 10px;
  min-width: 100%;
  padding: 0;
  width: 100%;
}

#contact input[type="text"],
#contact input[type="email"],
#contact input[type="tel"],
#contact input[type="url"],
#contact textarea {
  width: 100%;
  border: 1px solid #ccc;
  background: #eee;
  margin: 0 0 5px;
  padding: 10px;
}

#contact input[type="text"]:hover,
#contact input[type="email"]:hover,
#contact input[type="tel"]:hover,
#contact input[type="url"]:hover,
#contact textarea:hover {
  -webkit-transition: border-color 0.3s ease-in-out;
  -moz-transition: border-color 0.3s ease-in-out;
  transition: border-color 0.3s ease-in-out;
  border: 1px solid #aaa;
}

#contact textarea {
  height: 100px;
  max-width: 100%;
  resize: none;
}

#contact button[type="submit"] {
  cursor: pointer;
  width: 100%;
  border: none;
  background: #4CAF50;
  color: #FFF;
  margin: 0 0 5px;
  padding: 10px;
  font-size: 15px;
}

#contact button[type="submit"]:hover {
  background: #43A047;
  -webkit-transition: background 0.3s ease-in-out;
  -moz-transition: background 0.3s ease-in-out;
  transition: background-color 0.3s ease-in-out;
}

#contact button[type="submit"]:active {
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
}

.copyright {
  text-align: center;
}

#contact input:focus,
#contact textarea:focus {
  outline: 0;
  border: 1px solid #aaa;
}

::-webkit-input-placeholder {
  color: #888;
}

:-moz-placeholder {
  color: #888;
}

::-moz-placeholder {
  color: #888;
}

:-ms-input-placeholder {
  color: #888;
}
</style>
    <title>Playlist Creator</title>

</head>

<body>

  <div class="container">  
  
  <form id="contact" action="javascript:" onsubmit="saveStaticDataToFile(this)" method="post">
      
    <h2>Playlist Creator for Telegram Index</h2>
    <h4>Organize your telegram indexes</h4>
<!-- 
    <ul class="tabs" data-tabs id="example-tabs">
  <li class="tabs-title is-active"><a href="#contact" aria-selected="true">Series </a></li>
  <li class="tabs-title"><a href="#contact2">Movie </a></li>
  </ul> -->

    <fieldset>
      <input id="indexSite" placeholder="index.tg.repl.co" type="text" tabindex="1" required>
    </fieldset>
    <fieldset>
      <input id="playlistName" placeholder="Playlist Name" type="text" tabindex="1" required autofocus>
    </fieldset>
    <fieldset>
      <input id="duration" placeholder="Approx. duration of a single episode (in minutes)" type="tel" tabindex="2" required>
    </fieldset>
    <fieldset>
      <input id="seasonNum" placeholder="Season" type="tel" tabindex="3" >
    </fieldset>
    <fieldset>
      <input id="firstEpId" placeholder="First Episode Id Number" type="tel" tabindex="4">
    </fieldset>
    <fieldset>
      <input id="totaleps" placeholder="Total Episode in this Season" type="tel" tabindex="5">
    </fieldset>
    <fieldset>
      <button name="submit" type="submit" id="contact-submit" data-submit="...Sending">Create Playlist</button>
    </fieldset>
    <p class="copyright">Designed by <a href="https://github.com/rayanfer32" target="_blank" title="Rayanfer32 github">Rayanfer32</a></p>

</div>
</body>

<script>
  //# sourceMappingURL=FileSaver.min.js.map
  (function(a,b){if("function"==typeof define&&define.amd)define([],b);else if("undefined"!=typeof exports)b();else{b(),a.FileSaver={exports:{}}.exports}})(this,function(){"use strict";function b(a,b){return"undefined"==typeof b?b={autoBom:!1}:"object"!=typeof b&&(console.warn("Deprecated: Expected third argument to be a object"),b={autoBom:!b}),b.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(a.type)?new Blob(["\uFEFF",a],{type:a.type}):a}function c(a,b,c){var d=new XMLHttpRequest;d.open("GET",a),d.responseType="blob",d.onload=function(){g(d.response,b,c)},d.onerror=function(){console.error("could not download file")},d.send()}function d(a){var b=new XMLHttpRequest;b.open("HEAD",a,!1);try{b.send()}catch(a){}return 200<=b.status&&299>=b.status}function e(a){try{a.dispatchEvent(new MouseEvent("click"))}catch(c){var b=document.createEvent("MouseEvents");b.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),a.dispatchEvent(b)}}var f="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof global&&global.global===global?global:void 0,a=/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),g=f.saveAs||("object"!=typeof window||window!==f?function(){}:"download"in HTMLAnchorElement.prototype&&!a?function(b,g,h){var i=f.URL||f.webkitURL,j=document.createElement("a");g=g||b.name||"download",j.download=g,j.rel="noopener","string"==typeof b?(j.href=b,j.origin===location.origin?e(j):d(j.href)?c(b,g,h):e(j,j.target="_blank")):(j.href=i.createObjectURL(b),setTimeout(function(){i.revokeObjectURL(j.href)},4E4),setTimeout(function(){e(j)},0))}:"msSaveOrOpenBlob"in navigator?function(f,g,h){if(g=g||f.name||"download","string"!=typeof f)navigator.msSaveOrOpenBlob(b(f,h),g);else if(d(f))c(f,g,h);else{var i=document.createElement("a");i.href=f,i.target="_blank",setTimeout(function(){e(i)})}}:function(b,d,e,g){if(g=g||open("","_blank"),g&&(g.document.title=g.document.body.innerText="downloading..."),"string"==typeof b)return c(b,d,e);var h="application/octet-stream"===b.type,i=/constructor/i.test(f.HTMLElement)||f.safari,j=/CriOS\/[\d]+/.test(navigator.userAgent);if((j||h&&i||a)&&"undefined"!=typeof FileReader){var k=new FileReader;k.onloadend=function(){var a=k.result;a=j?a:a.replace(/^data:[^;]*;/,"data:attachment/file;"),g?g.location.href=a:location=a,g=null},k.readAsDataURL(b)}else{var l=f.URL||f.webkitURL,m=l.createObjectURL(b);g?g.location=m:location.href=m,g=null,setTimeout(function(){l.revokeObjectURL(m)},4E4)}});f.saveAs=g.saveAs=g,"undefined"!=typeof module&&(module.exports=g)});
</script>

<script>

// set value for indexSite as host 
const indexSiteEl = document.getElementById('indexSite')
indexSite.value = window.location.host

function createPlaylist(indexSite,playlistName = "Playlist",duration,seasonNum,firstEpId,totaleps){
  var pd = ""
  name = playlistName
  pd += '#EXTM3U\n'
  console.log(totaleps)
  for(let epNum=1;epNum<=totaleps;epNum++){
    meta = `#EXTINF: ${duration*60},S${seasonNum} EP${epNum} | ${name}\n`
    link = `http://${indexSite}/${firstEpId+epNum-1}/download\n`
    pd += meta
    pd += link
  }
  return pd
}

// collect data from form and export to m3u file
function saveStaticDataToFile(formdata) {
    playlistData = createPlaylist(formdata.indexSite.value,
    formdata.playlistName.value,
    parseInt(formdata.duration.value),
    parseInt(formdata.seasonNum.value),
    parseInt(formdata.firstEpId.value),
    parseInt(formdata.totaleps.value));

    var blob = new Blob([playlistData],{endings: "native"});
    saveAs(blob,`${formdata.playlistName.value}.m3u8`);
}
</script>

</html>
